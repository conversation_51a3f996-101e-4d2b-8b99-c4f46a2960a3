/* ===== GREENCHAIN ECOFUND - НОВЫЙ ПРЕМИУМ ДИЗАЙН ===== */

/* ===== ИМПОРТ ШРИФТОВ ===== */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

/* ===== CSS ПЕРЕМЕННЫЕ - ЭКО-МАЙНИНГ ПАЛИТРА ===== */
:root {
    /* Основные зеленые цвета */
    --primary-green: #16a34a;
    --primary-green-light: #22c55e;
    --primary-green-dark: #15803d;
    --primary-green-darker: #166534;
    --primary-green-darkest: #14532d;

    /* Белые и светлые цвета */
    --primary-white: #ffffff;
    --primary-light: #f8fafc;
    --primary-gray-50: #f9fafb;
    --primary-gray-100: #f3f4f6;
    --primary-gray-200: #e5e7eb;

    /* Зеленые акценты (заменили синие) */
    --accent-green: #16a34a;
    --accent-green-light: #22c55e;
    --accent-green-dark: #15803d;
    --accent-green-darker: #166534;

    /* Текстовые цвета для WCAG соответствия */
    --text-white: #ffffff;
    --text-light: #f1f5f9;
    --text-gray-light: #e2e8f0;
    --text-gray-medium: #94a3b8;
    --text-dark: #1f2937;
    --text-dark-medium: #374151;
    --text-dark-light: #4b5563;

    /* Статусные цвета */
    --success-green: #22c55e;
    --warning-yellow: #f59e0b;
    --danger-red: #ef4444;
    --info-green: #16a34a;

    /* Градиенты */
    --gradient-primary: linear-gradient(135deg, var(--primary-green-dark) 0%, var(--primary-green-darker) 100%);
    --gradient-header: linear-gradient(135deg, var(--primary-green-darkest) 0%, var(--primary-green-dark) 100%);
    --gradient-footer: linear-gradient(135deg, var(--primary-green-dark) 0%, var(--primary-green-darkest) 100%);
    --gradient-green: linear-gradient(135deg, var(--accent-green) 0%, var(--accent-green-dark) 100%);
    --gradient-eco: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-green-light) 100%);
    
    /* Тени */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-green: 0 0 20px rgba(22, 163, 74, 0.3);
    --shadow-green-accent: 0 0 20px rgba(22, 163, 74, 0.4);
    
    /* Переходы */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    /* Размеры */
    --border-radius-sm: 0.375rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    
    /* Шрифты */
    --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
}

/* ===== БАЗОВЫЕ СТИЛИ ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-dark);
    background: var(--primary-white);
    min-height: 100vh;
    overflow-x: hidden;
}

/* ===== ГЛОБАЛЬНЫЕ СТИЛИ ДЛЯ ВСЕХ СТРАНИЦ ===== */
.bg-light {
    background: var(--primary-light) !important;
    color: var(--text-dark) !important;
}

.bg-white {
    background: var(--primary-white) !important;
    color: var(--text-dark) !important;
}

/* Светлые секции */
section.bg-light,
.bg-light {
    background: var(--primary-light) !important;
    color: var(--text-dark) !important;
}

/* Белые карточки */
.card,
.step-card,
.feature-card {
    background: var(--primary-white) !important;
    border: 1px solid var(--primary-gray-200) !important;
    color: var(--text-dark) !important;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    border-radius: 12px;
}

.card:hover,
.step-card:hover,
.feature-card:hover {
    transform: translateY(-5px);
    border-color: var(--accent-green) !important;
    box-shadow: var(--shadow-lg);
}

/* ===== ГЛОБАЛЬНЫЕ СТИЛИ ТЕКСТА ===== */
h1, h2, h3, h4, h5, h6 {
    color: var(--text-dark) !important;
    font-weight: 600;
}

p {
    color: var(--text-dark-medium) !important;
    line-height: 1.6;
}

.text-white {
    color: var(--text-white) !important;
}

.text-light {
    color: var(--text-light) !important;
}

.text-muted {
    color: var(--text-gray-medium) !important;
}

.text-dark {
    color: var(--text-dark) !important;
}

.text-green {
    color: var(--primary-green) !important;
}

.text-green-accent {
    color: var(--accent-green) !important;
}

/* ===== ГЛОБАЛЬНЫЕ СТИЛИ КНОПОК ===== */
.btn {
    border-radius: 8px !important;
    font-weight: 600 !important;
    padding: 0.75rem 1.5rem !important;
    transition: all 0.3s ease !important;
    border: none;
}

.btn-primary {
    background: var(--gradient-green) !important;
    color: var(--text-white) !important;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px) !important;
    box-shadow: var(--shadow-green-accent) !important;
    background: linear-gradient(135deg, var(--accent-green-dark), var(--accent-green)) !important;
    color: var(--text-white) !important;
}

.btn-secondary {
    background: var(--primary-white) !important;
    border: 2px solid var(--accent-green) !important;
    color: var(--accent-green) !important;
}

.btn-secondary:hover {
    background: var(--accent-green) !important;
    border-color: var(--accent-green) !important;
    transform: translateY(-2px) !important;
    color: var(--text-white) !important;
}

.btn-success {
    background: var(--gradient-eco) !important;
    color: var(--text-white) !important;
}

.btn-success:hover {
    background: linear-gradient(135deg, var(--primary-green-dark), var(--primary-green)) !important;
    transform: translateY(-2px) !important;
    color: var(--text-white) !important;
}

/* ===== АДАПТИВНЫЕ СТИЛИ ===== */
@media (max-width: 1200px) {
    .container {
        max-width: 100%;
        padding: 0 2rem;
    }

    .section-title {
        font-size: 2.2rem;
    }
}

@media (max-width: 992px) {
    .section-title {
        font-size: 2rem;
    }

    .section-subtitle {
        font-size: 1.1rem;
    }

    .luxury-investment-rate {
        font-size: 2.5rem;
    }

    .btn {
        padding: 0.6rem 1.2rem !important;
        font-size: 0.9rem !important;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 1rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .section-subtitle {
        font-size: 1rem;
    }

    .feature-card,
    .step-card,
    .stats-card,
    .investment-card {
        margin-bottom: 1.5rem;
    }

    .luxury-investment-rate {
        font-size: 2rem;
    }

    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .row {
        margin-left: -10px;
        margin-right: -10px;
    }

    .col-lg-4,
    .col-lg-6,
    .col-md-6,
    .col-md-4 {
        padding-left: 10px;
        padding-right: 10px;
    }
}

@media (max-width: 576px) {
    .section-title {
        font-size: 1.5rem;
    }

    .luxury-investment-card {
        padding: 1.5rem;
    }

    .feature-card,
    .step-card {
        padding: 1.5rem 1rem;
    }
}

/* ===== ТИПОГРАФИКА ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

h1 { font-size: var(--font-size-5xl); }
h2 { font-size: var(--font-size-4xl); }
h3 { font-size: var(--font-size-3xl); }
h4 { font-size: var(--font-size-2xl); }
h5 { font-size: var(--font-size-xl); }
h6 { font-size: var(--font-size-lg); }

p {
    margin-bottom: 1rem;
    color: var(--text-dark-medium);
}

a {
    color: var(--accent-green);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--accent-green-dark);
    text-decoration: none;
}

/* ===== УТИЛИТАРНЫЕ КЛАССЫ ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-white { color: var(--text-white) !important; }
.text-green { color: var(--primary-green) !important; }
.text-green-accent { color: var(--accent-green) !important; }
.text-success { color: var(--success-green) !important; }
.text-warning { color: var(--warning-yellow) !important; }
.text-danger { color: var(--danger-red) !important; }

.bg-white { background-color: var(--primary-white) !important; }
.bg-light { background-color: var(--primary-light) !important; }
.bg-green { background-color: var(--primary-green) !important; }
.bg-green-accent { background-color: var(--accent-green) !important; }
.bg-gradient { background: var(--gradient-primary) !important; }
.bg-gradient-header { background: var(--gradient-header) !important; }
.bg-gradient-footer { background: var(--gradient-footer) !important; }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-green { box-shadow: var(--shadow-green); }
.shadow-green-accent { box-shadow: var(--shadow-green-accent); }

/* ===== УНИФИКАЦИЯ ОТСТУПОВ СЕКЦИЙ ===== */
section {
    padding: 4rem 0;
}

.py-5 {
    padding-top: 4rem !important;
    padding-bottom: 4rem !important;
}

.section-spacing {
    padding: 4rem 0;
}

/* Исключения для специальных секций */
.hero-section {
    padding: 6rem 0;
    min-height: 100vh;
}

.footer {
    padding: 3rem 0 1rem;
}

/* ===== КОНТРАСТНОСТЬ ТЕКСТА WCAG 2.1 AA ===== */
/* На темных фонах (header, footer) */
.header,
.footer,
.hero-section {
    color: var(--text-white);
}

.header h1, .header h2, .header h3, .header h4, .header h5, .header h6,
.footer h1, .footer h2, .footer h3, .footer h4, .footer h5, .footer h6,
.hero-section h1, .hero-section h2, .hero-section h3, .hero-section h4, .hero-section h5, .hero-section h6 {
    color: var(--text-white) !important;
}

.header p,
.footer p,
.hero-section p {
    color: var(--text-light) !important;
}

/* На светлых фонах (основной контент) */
.bg-white,
.bg-light,
section:not(.hero-section):not(.footer):not(.header) {
    color: var(--text-dark);
}

.bg-white h1, .bg-white h2, .bg-white h3, .bg-white h4, .bg-white h5, .bg-white h6,
.bg-light h1, .bg-light h2, .bg-light h3, .bg-light h4, .bg-light h5, .bg-light h6 {
    color: var(--text-dark) !important;
}

.bg-white p,
.bg-light p {
    color: var(--text-dark-medium) !important;
}

/* Обеспечиваем контраст для ссылок */
.bg-white a,
.bg-light a {
    color: var(--accent-green);
}

.bg-white a:hover,
.bg-light a:hover {
    color: var(--accent-green-dark);
}

.header a,
.footer a,
.hero-section a {
    color: var(--text-light);
}

.header a:hover,
.footer a:hover,
.hero-section a:hover {
    color: var(--text-white);
}

/* ===== ДОПОЛНИТЕЛЬНАЯ КОНТРАСТНОСТЬ WCAG ===== */
/* Обеспечиваем высокий контраст для зеленых элементов */
.text-green-contrast {
    color: #15803d !important; /* Темно-зеленый для лучшего контраста на белом */
}

.bg-green-contrast {
    background-color: #15803d !important;
    color: var(--text-white) !important;
}

/* Контрастные кнопки */
.btn-green-contrast {
    background: #15803d !important;
    color: var(--text-white) !important;
    border: none;
}

.btn-green-contrast:hover {
    background: #166534 !important;
    color: var(--text-white) !important;
    transform: translateY(-2px);
}

/* Контрастные ссылки */
.link-green-contrast {
    color: #15803d !important;
    text-decoration: underline;
}

.link-green-contrast:hover {
    color: #166534 !important;
    text-decoration: none;
}

.rounded-sm { border-radius: var(--border-radius-sm); }
.rounded-md { border-radius: var(--border-radius-md); }
.rounded-lg { border-radius: var(--border-radius-lg); }
.rounded-xl { border-radius: var(--border-radius-xl); }

/* ===== АНИМАЦИИ ===== */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes glow {
    0%, 100% { box-shadow: 0 0 5px var(--accent-green); }
    50% { box-shadow: 0 0 20px var(--accent-green), 0 0 30px var(--accent-green); }
}

@keyframes greenGlow {
    0%, 100% { box-shadow: 0 0 10px rgba(22, 163, 74, 0.3); }
    50% { box-shadow: 0 0 25px rgba(22, 163, 74, 0.6), 0 0 35px rgba(22, 163, 74, 0.4); }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-fadeIn { animation: fadeIn 0.6s ease-out; }
.animate-fadeInUp { animation: fadeInUp 0.8s ease-out; }
.animate-slideInLeft { animation: slideInLeft 0.6s ease-out; }
.animate-slideInRight { animation: slideInRight 0.6s ease-out; }
.animate-bounceIn { animation: bounceIn 0.8s ease-out; }
.animate-pulse { animation: pulse 2s infinite; }
.animate-glow { animation: glow 2s infinite; }
.animate-greenGlow { animation: greenGlow 2s infinite; }

/* ===== HOVER ЭФФЕКТЫ ===== */
.hover-lift {
    transition: var(--transition-normal);
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.hover-glow {
    transition: var(--transition-normal);
}

.hover-glow:hover {
    box-shadow: var(--shadow-green-accent);
    transform: scale(1.02);
}

.hover-green {
    transition: var(--transition-normal);
}

.hover-green:hover {
    background: var(--gradient-eco) !important;
    transform: translateY(-3px);
    box-shadow: var(--shadow-green-accent);
}

.hover-scale {
    transition: var(--transition-normal);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-rotate {
    transition: var(--transition-normal);
}

.hover-rotate:hover {
    transform: rotate(5deg) scale(1.05);
}

/* ===== ПРЕЛОАДЕР ===== */
#preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.spinner {
    width: 60px;
    height: 60px;
    position: relative;
}

.spinner-ring {
    width: 100%;
    height: 100%;
    border: 3px solid transparent;
    border-top: 3px solid var(--accent-gold);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== ИНТЕРАКТИВНЫЕ ЭЛЕМЕНТЫ ===== */
.interactive-element {
    transition: all 0.3s ease;
    cursor: pointer;
}

.interactive-element:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.green-accent-hover:hover {
    color: var(--accent-green) !important;
    transition: color 0.3s ease;
}

.border-green-hover {
    transition: border-color 0.3s ease;
}

.border-green-hover:hover {
    border-color: var(--accent-green) !important;
}

.bg-green-hover {
    transition: background-color 0.3s ease;
}

.bg-green-hover:hover {
    background-color: rgba(22, 163, 74, 0.1) !important;
}

/* ===== КОНТЕЙНЕРЫ ===== */
.main-content {
    min-height: calc(100vh - 80px);
    padding-top: 80px;
}

.container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
}

@media (min-width: 576px) {
    .container-fluid {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
}

@media (min-width: 768px) {
    .container-fluid {
        padding-left: 2rem;
        padding-right: 2rem;
    }
}
