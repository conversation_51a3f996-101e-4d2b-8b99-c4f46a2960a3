<?php
$page_title = "Личный кабинет";

// Проверка авторизации
if (!isLoggedIn()) {
    redirect('index.php?page=login&redirect=' . urlencode($_SERVER['REQUEST_URI']));
}

$user = getCurrentUser();
$user_stats = getDashboardStats($user['id']);
$active_investments = getActiveInvestments($user['id']);
$recent_transactions = getRecentTransactions($user['id']);
$daily_profit = calculateUserDailyProfit($user['id']);
?>

<div class="container-fluid">
    <!-- Приветствие -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="welcome-header">
                <h2 class="welcome-title">
                    Добро пожаловать, <?php echo htmlspecialchars($user['first_name']); ?>! 
                    <span class="wave">👋</span>
                </h2>
                <p class="welcome-subtitle">
                    Последний вход: <?php echo $user['last_login'] ? date('d.m.Y в H:i', strtotime($user['last_login'])) : 'Первый вход'; ?>
                </p>
            </div>
        </div>
    </div>
    
    <!-- Основная статистика -->
    <div class="row g-4 mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="stats-card hover-lift animate-fadeInUp">
                <div class="stats-icon">
                    <i class="fas fa-wallet"></i>
                </div>
                <div class="stats-value user-balance" data-user-id="<?php echo $user['id']; ?>"
                     data-user-name="<?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>"
                     data-user-role="<?php echo $user['role']; ?>">
                    <?php echo formatMoney($user['balance']); ?>
                </div>
                <div class="stats-label">Доступный баланс</div>
                <div class="stats-change positive">
                    <i class="fas fa-arrow-up"></i> +<?php echo formatMoney($daily_profit); ?> сегодня
                </div>
                <div class="stats-action">
                    <a href="index.php?page=invest" class="btn btn-primary btn-sm hover-glow">
                        <i class="fas fa-plus me-1"></i> Инвестировать
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="stats-card hover-lift animate-fadeInUp" style="animation-delay: 0.1s;">
                <div class="stats-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stats-value"><?php echo formatMoney($user_stats['total_invested']); ?></div>
                <div class="stats-label">Всего инвестировано</div>
                <div class="stats-change">
                    <i class="fas fa-briefcase me-1"></i><?php echo $user_stats['active_investments']; ?> активных инвестиций
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="stats-card hover-lift animate-fadeInUp" style="animation-delay: 0.2s;">
                <div class="stats-icon">
                    <i class="fas fa-coins"></i>
                </div>
                <div class="stats-value total-profit"><?php echo formatMoney($user_stats['total_profit']); ?></div>
                <div class="stats-label">Общая прибыль</div>
                    <div class="stats-change positive">
                        <i class="fas fa-percentage"></i> <?php echo formatPercent($user_stats['roi_percentage']); ?> ROI
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="stats-content">
                    <div class="stats-value"><?php echo formatMoney($user_stats['total_withdrawn']); ?></div>
                    <div class="stats-label">Выведено средств</div>
                    <div class="stats-change">
                        <?php echo $user_stats['withdrawal_count']; ?> операций
                    </div>
                </div>
                <div class="stats-action">
                    <a href="index.php?page=withdraw" class="btn btn-success btn-sm">
                        <i class="fas fa-download"></i> Вывести
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Дневная прибыль и быстрые действия -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-area text-success"></i> Ежедневная прибыль
                    </h5>
                </div>
                <div class="card-body">
                    <div class="profit-summary">
                        <div class="profit-today">
                            <h3 class="daily-profit"><?php echo formatMoney($daily_profit); ?></h3>
                            <p class="text-muted">Прибыль сегодня</p>
                        </div>
                        <div class="profit-chart">
                            <canvas id="profitChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt text-warning"></i> Быстрые действия
                    </h5>
                </div>
                <div class="card-body">
                    <div class="quick-actions">
                        <a href="index.php?page=invest" class="quick-action-btn">
                            <i class="fas fa-chart-line"></i>
                            <span>Инвестировать</span>
                        </a>
                        <a href="index.php?page=withdraw" class="quick-action-btn">
                            <i class="fas fa-money-bill-wave"></i>
                            <span>Вывести средства</span>
                        </a>
                        <a href="index.php?page=referrals" class="quick-action-btn">
                            <i class="fas fa-users"></i>
                            <span>Рефералы</span>
                        </a>
                        <a href="index.php?page=profile" class="quick-action-btn">
                            <i class="fas fa-user-cog"></i>
                            <span>Настройки</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Активные инвестиции -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-briefcase text-primary"></i> Активные инвестиции
                    </h5>
                    <a href="index.php?page=invest" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus"></i> Новая инвестиция
                    </a>
                </div>
                <div class="card-body">
                    <?php if (empty($active_investments)): ?>
                        <div class="empty-state">
                            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                            <h5>У вас пока нет активных инвестиций</h5>
                            <p class="text-muted">Начните инвестировать и получайте ежедневную прибыль!</p>
                            <a href="index.php?page=invest" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Создать первую инвестицию
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="investments-grid" id="active-investments">
                            <?php foreach ($active_investments as $investment): ?>
                                <div class="investment-card" data-investment-id="<?php echo $investment['id']; ?>">
                                    <div class="investment-header">
                                        <h6 class="investment-title"><?php echo htmlspecialchars($investment['package_name']); ?></h6>
                                        <span class="investment-type badge bg-<?php echo $investment['package_type'] === 'fixed' ? 'primary' : 'secondary'; ?>">
                                            <?php echo $investment['package_type'] === 'fixed' ? 'Фиксированный' : 'Гибкий'; ?>
                                        </span>
                                    </div>
                                    
                                    <div class="investment-amount">
                                        <?php echo formatMoney($investment['amount']); ?>
                                    </div>
                                    
                                    <div class="investment-stats">
                                        <div class="stat">
                                            <span class="label">Доходность:</span>
                                            <span class="value"><?php echo formatPercent($investment['daily_rate']); ?>/день</span>
                                        </div>
                                        <div class="stat">
                                            <span class="label">Заработано:</span>
                                            <span class="value current-profit"><?php echo formatMoney($investment['total_profit']); ?></span>
                                        </div>
                                        <?php if ($investment['end_date']): ?>
                                            <div class="stat">
                                                <span class="label">До окончания:</span>
                                                <span class="value time-left"><?php echo formatTimeLeft($investment['time_left']); ?></span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <?php if ($investment['end_date']): ?>
                                        <div class="investment-progress">
                                            <div class="progress">
                                                <div class="progress-bar" style="width: <?php echo $investment['progress']; ?>%"></div>
                                            </div>
                                            <small class="text-muted"><?php echo number_format($investment['progress'], 1); ?>% завершено</small>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="investment-actions">
                                        <button class="btn btn-sm btn-outline-primary" onclick="showInvestmentDetails(<?php echo $investment['id']; ?>)">
                                            <i class="fas fa-eye"></i> Детали
                                        </button>
                                        <?php if ($investment['package_type'] === 'flexible'): ?>
                                            <button class="btn btn-sm btn-outline-danger" onclick="withdrawInvestment(<?php echo $investment['id']; ?>)">
                                                <i class="fas fa-times"></i> Закрыть
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Последние транзакции -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history text-info"></i> Последние транзакции
                    </h5>
                    <a href="index.php?page=transactions" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-list"></i> Все транзакции
                    </a>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_transactions)): ?>
                        <div class="text-center text-muted py-3">
                            <i class="fas fa-receipt fa-2x mb-2"></i>
                            <p>Транзакций пока нет</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Дата</th>
                                        <th>Тип</th>
                                        <th>Сумма</th>
                                        <th>Статус</th>
                                        <th>Описание</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_transactions as $transaction): ?>
                                        <tr>
                                            <td><?php echo date('d.m.Y H:i', strtotime($transaction['created_at'])); ?></td>
                                            <td>
                                                <span class="transaction-type <?php echo $transaction['type']; ?>">
                                                    <i class="<?php echo getTransactionIcon($transaction['type']); ?>"></i>
                                                    <?php echo getTransactionTypeName($transaction['type']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="amount <?php echo in_array($transaction['type'], ['profit', 'referral_bonus', 'task_reward', 'deposit']) ? 'positive' : 'negative'; ?>">
                                                    <?php echo in_array($transaction['type'], ['profit', 'referral_bonus', 'task_reward', 'deposit']) ? '+' : '-'; ?>
                                                    <?php echo formatMoney($transaction['amount']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo getStatusColor($transaction['status']); ?>">
                                                    <?php echo getStatusName($transaction['status']); ?>
                                                </span>
                                            </td>
                                            <td class="description"><?php echo htmlspecialchars($transaction['description']); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.welcome-header {
    background: var(--primary-gradient);
    color: white;
    padding: 2rem;
    border-radius: 1rem;
    margin-bottom: 1rem;
}

.welcome-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.wave {
    animation: wave 2s infinite;
    transform-origin: 70% 70%;
    display: inline-block;
}

@keyframes wave {
    0% { transform: rotate(0deg); }
    10% { transform: rotate(14deg); }
    20% { transform: rotate(-8deg); }
    30% { transform: rotate(14deg); }
    40% { transform: rotate(-4deg); }
    50% { transform: rotate(10deg); }
    60% { transform: rotate(0deg); }
    100% { transform: rotate(0deg); }
}

.stats-card {
    background: rgba(26, 26, 26, 0.9);
    border: 1px solid rgba(212, 175, 55, 0.3);
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    height: 100%;
    position: relative;
    overflow: hidden;
    color: var(--text-white);
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.balance-card::before {
    background: var(--success-gradient);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--primary-gradient);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.balance-card .stats-icon {
    background: var(--success-gradient);
}

.stats-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.25rem;
}

.stats-label {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.stats-change {
    font-size: 0.8rem;
    color: #6c757d;
}

.stats-change.positive {
    color: var(--success-color);
}

.stats-action {
    margin-top: 1rem;
}

.profit-summary {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.profit-today h3 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--success-color);
    margin-bottom: 0;
}

.quick-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 0.5rem;
    text-decoration: none;
    color: var(--dark-color);
    transition: all 0.3s ease;
}

.quick-action-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.quick-action-btn i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.investments-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.investment-card {
    background: rgba(26, 26, 26, 0.9);
    border: 1px solid rgba(212, 175, 55, 0.3);
    border-radius: 1rem;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    color: var(--text-white);
}

.investment-card:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.investment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.investment-title {
    font-weight: 600;
    margin: 0;
}

.investment-amount {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.investment-stats {
    margin-bottom: 1rem;
}

.investment-stats .stat {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.investment-stats .label {
    color: #6c757d;
    font-size: 0.9rem;
}

.investment-stats .value {
    font-weight: 600;
}

.investment-progress {
    margin-bottom: 1rem;
}

.investment-actions {
    display: flex;
    gap: 0.5rem;
}

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
}

.transaction-type {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.amount.positive {
    color: var(--success-color);
}

.amount.negative {
    color: var(--danger-color);
}

.description {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

@media (max-width: 768px) {
    .profit-summary {
        flex-direction: column;
        text-align: center;
    }
    
    .quick-actions {
        grid-template-columns: 1fr;
    }
    
    .investments-grid {
        grid-template-columns: 1fr;
    }
    
    .investment-actions {
        flex-direction: column;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Инициализация графика прибыли
    initializeProfitChart();
    
    // Запуск обновлений в реальном времени
    if (typeof startRealTimeUpdates === 'function') {
        startRealTimeUpdates();
    }
});

function initializeProfitChart() {
    const ctx = document.getElementById('profitChart');
    if (!ctx) return;
    
    // Получаем данные за последние 7 дней
    fetch('api/profit-chart.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                createProfitChart(ctx, data.chartData);
            }
        })
        .catch(error => {
            console.error('Error loading profit chart:', error);
        });
}

function createProfitChart(ctx, data) {
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.labels,
            datasets: [{
                label: 'Ежедневная прибыль',
                data: data.profits,
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toFixed(2);
                        }
                    }
                }
            }
        }
    });
}

function showInvestmentDetails(investmentId) {
    // Показать детали инвестиции
    fetch(`api/investment-details.php?id=${investmentId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showInvestmentModal(data.investment);
            }
        })
        .catch(error => {
            console.error('Error loading investment details:', error);
        });
}

function withdrawInvestment(investmentId) {
    if (confirm('Вы уверены, что хотите закрыть эту инвестицию? Основная сумма будет возвращена на ваш баланс.')) {
        fetch('api/close-investment.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                investment_id: investmentId,
                csrf_token: '<?php echo generateCSRFToken(); ?>'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error closing investment:', error);
            showNotification('Произошла ошибка. Попробуйте позже.', 'error');
        });
    }
}
</script>

<?php
/**
 * Получение статистики для дашборда
 */
function getDashboardStats($user_id) {
    global $conn;
    
    $stmt = $conn->prepare("
        SELECT 
            COALESCE(SUM(CASE WHEN ui.status = 'active' THEN ui.amount ELSE 0 END), 0) as total_invested,
            COALESCE(SUM(ui.total_profit), 0) as total_profit,
            COUNT(CASE WHEN ui.status = 'active' THEN 1 END) as active_investments,
            COALESCE(
                CASE 
                    WHEN SUM(CASE WHEN ui.status = 'active' THEN ui.amount ELSE 0 END) > 0 
                    THEN (SUM(ui.total_profit) / SUM(CASE WHEN ui.status = 'active' THEN ui.amount ELSE 0 END)) * 100
                    ELSE 0 
                END, 0
            ) as roi_percentage,
            (SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE user_id = ? AND type = 'withdrawal' AND status = 'completed') as total_withdrawn,
            (SELECT COUNT(*) FROM transactions WHERE user_id = ? AND type = 'withdrawal') as withdrawal_count
        FROM user_investments ui
        WHERE ui.user_id = ?
    ");
    $stmt->execute([$user_id, $user_id, $user_id]);
    
    return $stmt->fetch();
}

/**
 * Получение активных инвестиций с деталями
 */
function getActiveInvestments($user_id) {
    global $conn;
    
    $stmt = $conn->prepare("
        SELECT 
            ui.*,
            ip.name as package_name,
            ip.type as package_type,
            CASE 
                WHEN ui.end_date IS NULL THEN 100
                ELSE ROUND((DATEDIFF(CURDATE(), ui.start_date) / DATEDIFF(ui.end_date, ui.start_date)) * 100, 2)
            END as progress,
            CASE 
                WHEN ui.end_date IS NULL THEN NULL
                ELSE DATEDIFF(ui.end_date, CURDATE()) * 86400
            END as time_left
        FROM user_investments ui
        JOIN investment_packages ip ON ui.package_id = ip.id
        WHERE ui.user_id = ? AND ui.status = 'active'
        ORDER BY ui.created_at DESC
    ");
    $stmt->execute([$user_id]);
    
    return $stmt->fetchAll();
}

/**
 * Получение последних транзакций
 */
function getRecentTransactions($user_id) {
    global $conn;
    
    $stmt = $conn->prepare("
        SELECT * FROM transactions 
        WHERE user_id = ? 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $stmt->execute([$user_id]);
    
    return $stmt->fetchAll();
}

/**
 * Расчет дневной прибыли пользователя
 */
function calculateUserDailyProfit($user_id) {
    global $conn;
    
    $stmt = $conn->prepare("
        SELECT COALESCE(SUM(amount * (daily_rate / 100)), 0) as daily_profit
        FROM user_investments 
        WHERE user_id = ? AND status = 'active'
        AND (end_date IS NULL OR end_date >= CURDATE())
    ");
    $stmt->execute([$user_id]);
    $result = $stmt->fetch();
    
    return $result['daily_profit'];
}

/**
 * Форматирование оставшегося времени
 */
function formatTimeLeft($seconds) {
    if (!$seconds || $seconds <= 0) return 'Завершено';
    
    $days = floor($seconds / 86400);
    $hours = floor(($seconds % 86400) / 3600);
    
    if ($days > 0) {
        return $days . 'д ' . $hours . 'ч';
    } else {
        return $hours . 'ч';
    }
}

/**
 * Получение иконки для типа транзакции
 */
function getTransactionIcon($type) {
    $icons = [
        'deposit' => 'fas fa-plus-circle',
        'withdrawal' => 'fas fa-minus-circle',
        'investment' => 'fas fa-chart-line',
        'profit' => 'fas fa-coins',
        'referral_bonus' => 'fas fa-users',
        'task_reward' => 'fas fa-gift'
    ];
    
    return $icons[$type] ?? 'fas fa-circle';
}

/**
 * Получение названия типа транзакции
 */
function getTransactionTypeName($type) {
    $names = [
        'deposit' => 'Пополнение',
        'withdrawal' => 'Вывод',
        'investment' => 'Инвестиция',
        'profit' => 'Прибыль',
        'referral_bonus' => 'Реферальный бонус',
        'task_reward' => 'Награда'
    ];
    
    return $names[$type] ?? $type;
}
?>
