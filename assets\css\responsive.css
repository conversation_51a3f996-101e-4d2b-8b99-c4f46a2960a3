/* ===== АДАПТИВНЫЙ ДИЗАЙН ===== */

/* ===== МОБИЛЬНЫЕ УСТРОЙСТВА (до 576px) ===== */
@media (max-width: 575.98px) {
    /* Базовые настройки */
    html {
        font-size: 14px;
    }
    
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    /* Навигация */
    .navbar {
        padding: 0.75rem 0;
        min-height: 70px;
    }
    
    .brand-text {
        font-size: 1.25rem;
    }
    
    .brand-subtitle {
        font-size: 0.625rem;
        margin-left: 2.25rem;
    }
    
    .navbar-collapse {
        background: var(--gradient-header);
        border-radius: var(--border-radius-lg);
        margin-top: 1rem;
        padding: 1rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .navbar-nav {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .nav-link {
        padding: 1rem;
        border-radius: var(--border-radius-md);
        text-align: center;
    }
    
    .dropdown-menu {
        position: static;
        width: 100%;
        box-shadow: none;
        border: none;
        background: rgba(26, 61, 46, 0.1);
        margin-top: 0.5rem;
        border-radius: var(--border-radius-md);
    }
    
    /* Главная страница */
    .hero-section {
        min-height: calc(100vh - 70px);
        padding: 2rem 0;
    }
    
    .hero-title {
        font-size: 2rem;
        line-height: 1.2;
    }
    
    .hero-subtitle {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .hero-stats .row {
        gap: 1rem;
    }
    
    .hero-stats .col-4 {
        flex: 1;
        min-width: 0;
    }
    
    .stat-card {
        padding: 1rem;
    }
    
    .stat-number {
        font-size: 1.25rem;
    }
    
    .stat-label {
        font-size: 0.75rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .btn {
        width: 100%;
        justify-content: center;
    }
    
    /* Авторизация */
    .auth-container {
        padding: 1rem 0;
    }
    
    .auth-card {
        margin: 0;
        border-radius: var(--border-radius-lg);
    }
    
    .auth-header,
    .auth-body,
    .auth-footer {
        padding: 1.5rem 1rem;
    }
    
    .auth-header h3 {
        font-size: 1.5rem;
    }
    
    .auth-links {
        flex-direction: column;
        gap: 0.75rem;
        text-align: center;
    }
    
    /* Dashboard */
    .main-content {
        padding-top: 70px;
    }
    
    .welcome-header {
        padding: 1.5rem 1rem;
        margin-bottom: 1.5rem;
    }
    
    .welcome-title {
        font-size: 1.75rem;
    }
    
    .stats-card {
        margin-bottom: 1rem;
        padding: 1.25rem;
    }
    
    .stats-icon {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 1rem;
    }
    
    .stats-value {
        font-size: 1.5rem;
    }
    
    /* Карточки инвестиций */
    .investment-card {
        padding: 1.25rem;
        margin-bottom: 1rem;
    }
    
    .investment-header {
        flex-direction: column;
        gap: 0.75rem;
        align-items: flex-start;
    }
    
    .investment-details {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
    
    /* Формы */
    .form-control {
        padding: 1rem;
        font-size: 1rem;
    }
    
    .btn {
        padding: 1rem 1.5rem;
        font-size: 1rem;
    }
    
    /* Модальные окна */
    .modal-dialog {
        width: calc(100% - 2rem);
        margin: 1rem;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 1rem;
    }
    
    /* Footer */
    .footer {
        padding: 2rem 0 1rem;
    }
    
    .footer-widget {
        margin-bottom: 1.5rem;
        text-align: center;
    }
    
    .social-links {
        justify-content: center;
    }
    
    .footer-stats {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    /* Виджеты */
    .live-chat-widget {
        bottom: 1rem;
        left: 1rem;
    }
    
    .chat-window {
        width: calc(100vw - 2rem);
        max-width: 280px;
        height: 350px;
    }
    
    .btn-back-to-top {
        bottom: 1rem;
        right: 1rem;
        width: 2.5rem;
        height: 2.5rem;
        font-size: 1rem;
    }
}

/* ===== ПЛАНШЕТЫ (576px - 768px) ===== */
@media (min-width: 576px) and (max-width: 767.98px) {
    .container-fluid {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.125rem;
    }
    
    .hero-buttons {
        flex-direction: row;
        justify-content: center;
    }
    
    .auth-card {
        max-width: 500px;
        margin: 0 auto;
    }
    
    .stats-card {
        margin-bottom: 1.5rem;
    }
    
    .investment-details {
        grid-template-columns: 1fr 1fr;
    }
    
    .chat-window {
        width: 320px;
        height: 380px;
    }
}

/* ===== МАЛЫЕ ДЕСКТОПЫ (768px - 992px) ===== */
@media (min-width: 768px) and (max-width: 991.98px) {
    .container-fluid {
        padding-left: 2rem;
        padding-right: 2rem;
    }
    
    .hero-title {
        font-size: 3rem;
    }
    
    .navbar-nav {
        gap: 0.75rem;
    }
    
    .dropdown-menu {
        min-width: 220px;
    }
    
    .stats-card {
        margin-bottom: 2rem;
    }
    
    .investment-card {
        margin-bottom: 1.5rem;
    }
}

/* ===== БОЛЬШИЕ ДЕСКТОПЫ (992px - 1200px) ===== */
@media (min-width: 992px) and (max-width: 1199.98px) {
    .hero-title {
        font-size: 3.5rem;
    }
    
    .container-fluid {
        max-width: 1140px;
        margin: 0 auto;
    }
}

/* ===== ОЧЕНЬ БОЛЬШИЕ ЭКРАНЫ (1200px+) ===== */
@media (min-width: 1200px) {
    .hero-title {
        font-size: 4rem;
    }
    
    .container-fluid {
        max-width: 1320px;
        margin: 0 auto;
    }
    
    .hero-section {
        min-height: 100vh;
    }
    
    .stats-card:hover {
        transform: translateY(-8px);
    }
    
    .investment-card:hover {
        transform: translateY(-8px);
    }
}

/* ===== ЛАНДШАФТНАЯ ОРИЕНТАЦИЯ МОБИЛЬНЫХ ===== */
@media (max-height: 500px) and (orientation: landscape) {
    .hero-section {
        min-height: auto;
        padding: 4rem 0;
    }
    
    .auth-container {
        min-height: auto;
        padding: 2rem 0;
    }
    
    .main-content {
        padding-top: 70px;
    }
    
    .navbar {
        min-height: 60px;
    }
}

/* ===== ВЫСОКИЕ ЭКРАНЫ ===== */
@media (min-height: 900px) {
    .hero-section {
        min-height: 100vh;
    }
    
    .auth-container {
        min-height: 100vh;
    }
}

/* ===== ПЕЧАТЬ ===== */
@media print {
    .header,
    .footer,
    .btn-back-to-top,
    .live-chat-widget,
    .navbar-toggler {
        display: none !important;
    }
    
    .main-content {
        padding-top: 0;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
    
    .card,
    .stats-card,
    .investment-card {
        border: 1px solid #ccc !important;
        background: white !important;
        box-shadow: none !important;
    }
    
    .text-white,
    .text-light-gray {
        color: black !important;
    }
    
    .text-gold {
        color: #b8941f !important;
    }
}

/* ===== ДОСТУПНОСТЬ ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .animate-fadeIn,
    .animate-fadeInUp,
    .animate-slideInLeft,
    .animate-pulse,
    .animate-glow {
        animation: none !important;
    }
}

@media (prefers-contrast: high) {
    :root {
        --accent-gold: #ffff00;
        --text-white: #ffffff;
        --primary-black: #000000;
    }
    
    .card,
    .stats-card,
    .investment-card {
        border-width: 2px !important;
    }
    
    .btn {
        border-width: 2px !important;
    }
}

/* ===== ТЕМНАЯ ТЕМА (СИСТЕМНАЯ) ===== */
@media (prefers-color-scheme: dark) {
    /* Наши стили уже темные, но можно добавить дополнительные настройки */
    :root {
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
        --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
    }
}

/* ===== ДОПОЛНИТЕЛЬНЫЕ МОБИЛЬНЫЕ УЛУЧШЕНИЯ ===== */
@media (max-width: 575.98px) {
    /* Улучшенная навигация для мобильных */
    .navbar-collapse {
        margin-top: 1rem;
        padding: 1.5rem;
        background: rgba(0, 0, 0, 0.98);
        border-radius: var(--border-radius-lg);
        border: 1px solid rgba(212, 175, 55, 0.3);
        backdrop-filter: blur(15px);
    }

    .navbar-nav .nav-link {
        padding: 1rem 1.5rem;
        margin-bottom: 0.5rem;
        border-radius: var(--border-radius-md);
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: var(--transition-normal);
        color: var(--text-white);
    }

    .navbar-nav .nav-link:hover,
    .navbar-nav .nav-link.active {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.3);
        transform: translateX(5px);
        color: var(--text-white);
    }

    /* Улучшенные формы для мобильных */
    .form-control {
        padding: 1rem 1.25rem;
        font-size: 1rem;
        min-height: 3rem;
    }

    .btn {
        padding: 1rem 1.5rem;
        font-size: 1rem;
        min-height: 3rem;
    }

    /* Улучшенные карточки для мобильных */
    .card {
        margin-bottom: 1.5rem;
        border-radius: var(--border-radius-xl);
    }

    .stats-card {
        padding: 1.5rem;
        text-align: center;
    }

    .stats-icon {
        margin: 0 auto 1rem;
    }

    /* Улучшенные модальные окна для мобильных */
    .modal-dialog {
        margin: 0.5rem;
        width: calc(100% - 1rem);
    }

    .modal-content {
        border-radius: var(--border-radius-xl);
    }

    /* Улучшенный hero section для мобильных */
    .hero-section {
        padding: 2rem 0;
        min-height: calc(100vh - 70px);
    }

    .hero-stats .col-4 {
        margin-bottom: 1rem;
    }

    .stat-card {
        padding: 1rem 0.75rem;
        min-height: auto;
    }

    .stat-number {
        font-size: 1.125rem;
        margin-bottom: 0.25rem;
    }

    .stat-label {
        font-size: 0.75rem;
        line-height: 1.2;
    }

    /* Улучшенные кнопки для мобильных */
    .hero-buttons .btn {
        width: 100%;
        margin-bottom: 0.75rem;
        justify-content: center;
        font-weight: 600;
    }

    /* Улучшенный footer для мобильных */
    .footer {
        padding: 2rem 0 1rem;
    }

    .footer-widget {
        text-align: center;
        margin-bottom: 2rem;
    }

    .social-links {
        justify-content: center;
        margin-top: 1.5rem;
    }

    .social-link {
        width: 3rem;
        height: 3rem;
        font-size: 1.25rem;
    }

    /* Улучшенные виджеты для мобильных */
    .live-chat-widget {
        bottom: 1rem;
        left: 1rem;
        z-index: 1001;
    }

    .chat-toggle {
        width: 3.5rem;
        height: 3.5rem;
        font-size: 1.5rem;
        box-shadow: var(--shadow-xl);
    }

    .btn-back-to-top {
        bottom: 1rem;
        right: 1rem;
        width: 3rem;
        height: 3rem;
        font-size: 1.25rem;
        z-index: 1001;
    }
}

/* ===== УЛУЧШЕНИЯ ДЛЯ TOUCH УСТРОЙСТВ ===== */
@media (hover: none) and (pointer: coarse) {
    /* Увеличиваем размеры кликабельных элементов */
    .btn,
    .nav-link,
    .dropdown-item {
        min-height: 44px;
        padding: 0.75rem 1rem;
    }

    /* Убираем hover эффекты для touch устройств */
    .hover-lift:hover,
    .hover-glow:hover,
    .card:hover,
    .stats-card:hover {
        transform: none;
        box-shadow: var(--shadow-lg);
    }

    /* Добавляем активные состояния для touch */
    .btn:active {
        transform: scale(0.98);
    }

    .card:active,
    .stats-card:active {
        transform: scale(0.99);
    }
}
