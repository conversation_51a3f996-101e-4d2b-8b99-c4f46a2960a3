/* ===== НАВИГАЦИЯ И СТРУКТУРА ===== */

/* ===== HEADER ===== */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: var(--gradient-header);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: var(--transition-normal);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar {
    padding: 1rem 0;
    min-height: 80px;
}

.navbar-brand {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    text-decoration: none;
    color: var(--text-white);
    transition: var(--transition-fast);
}

.navbar-brand:hover {
    color: var(--text-light);
    text-decoration: none;
}

.brand-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.25rem;
}

.logo-icon {
    font-size: 2rem;
    filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
    color: var(--text-white);
}

.brand-text {
    font-size: 1.5rem;
    font-weight: 700;
    background: var(--gradient-gold);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.brand-subtitle {
    font-size: 0.75rem;
    color: var(--text-light-gray);
    margin-left: 2.75rem;
}

.navbar-toggler {
    border: none;
    background: none;
    color: var(--text-white);
    font-size: 1.5rem;
    padding: 0.5rem;
    transition: var(--transition-fast);
}

.navbar-toggler:hover {
    color: var(--accent-gold);
}

.navbar-nav {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    color: var(--text-light);
    text-decoration: none;
    border-radius: var(--border-radius-md);
    transition: var(--transition-fast);
    font-weight: 500;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--text-white);
    background: rgba(255, 255, 255, 0.1);
    text-decoration: none;
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 2px;
    background: var(--text-white);
    border-radius: 1px;
}

.balance-badge {
    background: var(--gradient-gold);
    color: var(--primary-black);
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    margin-left: 0.5rem;
}

/* ===== DROPDOWN MENU ===== */
.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    min-width: 250px;
    background: rgba(26, 26, 26, 0.95);
    border: 1px solid rgba(212, 175, 55, 0.2);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    backdrop-filter: blur(10px);
    padding: 0.5rem 0;
    margin-top: 0.5rem;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition-normal);
    z-index: 1050;
}

.dropdown.show .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    color: var(--text-light-gray);
    text-decoration: none;
    transition: var(--transition-fast);
    border: none;
    background: none;
    width: 100%;
    text-align: left;
}

.dropdown-item:hover {
    color: var(--accent-gold);
    background: rgba(212, 175, 55, 0.1);
    text-decoration: none;
}

.dropdown-divider {
    height: 1px;
    background: rgba(212, 175, 55, 0.2);
    margin: 0.5rem 0;
    border: none;
}

/* ===== FOOTER ===== */
.footer {
    background: var(--gradient-footer);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 3rem 0 1rem;
    margin-top: 4rem;
    color: var(--text-white);
}

.footer-widget {
    margin-bottom: 2rem;
}

.widget-title {
    color: var(--text-white);
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: 1rem;
    position: relative;
}

.widget-title::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 0;
    width: 3rem;
    height: 2px;
    background: var(--gradient-gold);
    border-radius: 1px;
}

.widget-text {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 0.75rem;
}

.footer-links a {
    color: var(--text-light);
    text-decoration: none;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.footer-links a:hover {
    color: var(--text-white);
    text-decoration: none;
    transform: translateX(5px);
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    color: var(--text-white);
    text-decoration: none;
    transition: var(--transition-normal);
}

.social-link:hover {
    background: var(--text-white);
    color: var(--primary-green-dark);
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
    text-decoration: none;
}

.contact-info p {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--text-light-gray);
    margin-bottom: 0.75rem;
}

.contact-info i {
    color: var(--accent-gold);
    width: 1.25rem;
}

.footer-divider {
    border: none;
    height: 1px;
    background: rgba(212, 175, 55, 0.2);
    margin: 2rem 0 1.5rem;
}

.copyright {
    color: var(--text-medium-gray);
    font-size: var(--font-size-sm);
    margin: 0;
}

.footer-stats {
    display: flex;
    gap: 2rem;
    justify-content: flex-end;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-light-gray);
    font-size: var(--font-size-sm);
}

.stat-item i {
    color: var(--accent-gold);
}

/* ===== BACK TO TOP BUTTON ===== */
.btn-back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 3rem;
    height: 3rem;
    background: var(--gradient-gold);
    color: var(--primary-black);
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    cursor: pointer;
    transition: var(--transition-normal);
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    z-index: 1000;
    box-shadow: var(--shadow-lg);
}

.btn-back-to-top.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.btn-back-to-top:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-gold);
}

/* ===== LIVE CHAT WIDGET ===== */
.live-chat-widget {
    position: fixed;
    bottom: 2rem;
    left: 2rem;
    z-index: 1000;
}

.chat-toggle {
    width: 3.5rem;
    height: 3.5rem;
    background: var(--gradient-gold);
    color: var(--primary-black);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    cursor: pointer;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-lg);
}

.chat-toggle:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-gold);
}

.chat-window {
    position: absolute;
    bottom: 4.5rem;
    left: 0;
    width: 300px;
    height: 400px;
    background: rgba(26, 26, 26, 0.95);
    border: 1px solid rgba(212, 175, 55, 0.3);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    backdrop-filter: blur(10px);
    display: flex;
    flex-direction: column;
}

.chat-header {
    padding: 1rem;
    border-bottom: 1px solid rgba(212, 175, 55, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-header h6 {
    margin: 0;
    color: var(--text-white);
    font-weight: 600;
}

.btn-close-chat {
    background: none;
    border: none;
    color: var(--text-light-gray);
    font-size: 1.5rem;
    cursor: pointer;
    transition: var(--transition-fast);
}

.btn-close-chat:hover {
    color: var(--accent-gold);
}

.chat-messages {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
}

.message {
    margin-bottom: 1rem;
    padding: 0.75rem;
    border-radius: var(--border-radius-md);
    max-width: 80%;
}

.bot-message {
    background: rgba(30, 58, 138, 0.2);
    color: var(--text-light-gray);
    margin-right: auto;
}

.user-message {
    background: rgba(212, 175, 55, 0.2);
    color: var(--text-white);
    margin-left: auto;
}

.chat-input {
    padding: 1rem;
    border-top: 1px solid rgba(212, 175, 55, 0.2);
    display: flex;
    gap: 0.5rem;
}

.chat-input input {
    flex: 1;
    padding: 0.5rem;
    background: rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(212, 175, 55, 0.3);
    border-radius: var(--border-radius-sm);
    color: var(--text-white);
    font-size: var(--font-size-sm);
}

.chat-input input:focus {
    outline: none;
    border-color: var(--accent-gold);
}

.chat-input button {
    padding: 0.5rem;
    background: var(--gradient-gold);
    color: var(--primary-black);
    border: none;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: var(--transition-fast);
}

.chat-input button:hover {
    background: var(--accent-bright-gold);
}
